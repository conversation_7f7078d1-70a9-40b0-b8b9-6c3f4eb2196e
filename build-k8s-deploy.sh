#!/bin/bash

set -e

# 解析命令行参数
DEPLOY_ALL=false
RESTART_ONLY=false
SHOW_HELP=false
NAMESPACE="trinax-system"
KUBECONFIG_PATH=""

while [[ $# -gt 0 ]]; do
  case $1 in
    --all)
      DEPLOY_ALL=true
      shift
      ;;
    --restart-only)
      RESTART_ONLY=true
      shift
      ;;
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --kubeconfig)
      KUBECONFIG_PATH="$2"
      shift 2
      ;;
    --help|-h)
      SHOW_HELP=true
      shift
      ;;
    *)
      echo "未知参数: $1"
      echo "使用 --help 查看帮助信息"
      exit 1
      ;;
  esac
done

# 显示帮助信息
if [[ "$SHOW_HELP" == "true" ]]; then
  echo "DTT-Trinasolar项目Kubernetes自动化构建部署脚本"
  echo ""
  echo "使用方法:"
  echo "  $0                          # 根据BUILD_PROJECTS配置构建和部署指定服务"
  echo "  $0 --all                    # 部署所有项目并重启所有服务"
  echo "  $0 --restart-only           # 仅重启服务，不重新构建镜像"
  echo "  $0 --namespace <ns>         # 指定Kubernetes命名空间"
  echo "  $0 --kubeconfig <path>      # 指定kubeconfig文件路径"
  echo "  $0 --help                   # 显示此帮助信息"
  echo ""
  echo "配置文件: conf/drb-deploy.conf"
  echo "Kubernetes配置: k8s/"
  exit 0
fi

# 载入配置
source ./conf/drb-deploy.conf

# 设置kubeconfig
if [[ -n "$KUBECONFIG_PATH" ]]; then
  export KUBECONFIG="$KUBECONFIG_PATH"
fi

# 检查kubectl连接
echo "🔍 检查Kubernetes集群连接..."
if ! kubectl cluster-info >/dev/null 2>&1; then
  echo "❌ 无法连接到Kubernetes集群，请检查kubeconfig配置"
  exit 1
fi

echo "✅ Kubernetes集群连接正常"
echo "📍 当前上下文: $(kubectl config current-context)"
echo "📍 目标命名空间: $NAMESPACE"

# 确保命名空间存在
echo "🔧 确保命名空间存在..."
kubectl apply -f k8s/base/namespace.yaml

# 如果是仅重启模式，跳过构建步骤
if [[ "$RESTART_ONLY" == "true" ]]; then
  echo "🔄 仅重启模式: 跳过代码拉取和镜像构建，直接重启服务..."
  
  if [[ "$DEPLOY_ALL" == "true" ]]; then
    echo "🚀 重启所有服务..."
    kubectl rollout restart deployment -n "$NAMESPACE"
  else
    echo "🚀 重启指定服务..."
    for project in "${BUILD_PROJECTS[@]}"; do
      deployment_name=$(get_k8s_deployment_name "$project")
      if kubectl get deployment "$deployment_name" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "🔄 重启部署: $deployment_name"
        kubectl rollout restart deployment "$deployment_name" -n "$NAMESPACE"
      else
        echo "⚠️  警告: 部署 $deployment_name 不存在"
      fi
    done
  fi
  
  echo "✅ 重启完成。"
  exit 0
fi

# 构建镜像（复用原有的构建逻辑）
echo "📦 开始构建Docker镜像..."
# 这里可以复用原有的build-deploy.sh中的镜像构建逻辑
# 或者调用原脚本但跳过docker-compose部分

# 清理工作目录
echo "📌 清理工作目录..."
find "$BASE_DIR" -mindepth 1 -maxdepth 1 \
  ! -name "conf" \
  ! -name "build-deploy.sh" \
  ! -name "build-all.sh" \
  ! -name "build-frontend.sh" \
  ! -name "build-k8s-deploy.sh" \
  ! -name "Dockerfile" \
  ! -name "Dockerfile-Frontend" \
  ! -name ".gitignore" \
  ! -name ".git" \
  ! -name "README.md" \
  ! -name "k8s" \
  ! -name "tmp_npm_cache" \
  ! -name "tmp-mvn-repo" \
  -exec rm -rf {} +

# 克隆代码
cd "$BASE_DIR"
repo_url="${GIT_REPO}"
echo "📦 克隆仓库: $repo_url"
git clone "$repo_url"

if [ ! -d "$PROJECT_DIR/.git" ]; then
  echo "❌ 克隆失败或非 git 仓库目录: $PROJECT_DIR"
  exit 1
fi

echo "🔀 切换 $PROJECT_DIR 分支: $BRANCH"
git -C "$PROJECT_DIR" checkout "$BRANCH"
git -C "$PROJECT_DIR" pull

# Maven构建（复用原有逻辑）
echo "📦 Maven 构建..."
cd "$PROJECT_DIR"

# 构建公共依赖
if [ -d "dtt-component" ]; then
  echo "➡️ 处理公共依赖目录: dtt-component"
  cd dtt-component
  mvn clean install -T 1C -Dmaven.test.skip=true -s "$MAVEN_SETTINGS"
  cd ..
fi

# 构建其他依赖...（省略详细步骤，与原脚本相同）

# 构建各个项目的Docker镜像
for proj in "${BUILD_PROJECTS[@]}"; do
  echo "📦 开始构建项目: $proj"
  
  # 推导jar包路径
  biz_dir=$(find . -name $proj)
  module_dir=$(echo $biz_dir | cut -d'/' -f2)
  
  echo "📦 module_dir名称: $module_dir"
  cd $module_dir
  mvn clean install -T 1C -Dmaven.test.skip=true -s "$MAVEN_SETTINGS"
  cd ..
  
  jar_path="${biz_dir}/target/$proj.jar"
  
  if [ ! -f "$jar_path" ]; then
    echo "❌ 没找到 $jar_path，跳过镜像构建"
    continue
  fi
  
  # 获取项目配置
  port=$(get_port_mapping "$proj")
  memory=$(get_memory_mapping "$proj")
  
  echo "🐳 Docker build 镜像 $proj, jar 路径 $jar_path, 端口 $port, 内存限制 $memory"
  docker build -f "$DOCKERFILE" \
    --build-arg JAR_PATH="$jar_path" \
    --build-arg EXPOSE_PORT="$port" \
    --build-arg MEMORY_LIMIT="$memory" \
    -t "$proj:latest" .
  
  cd "$PROJECT_DIR"
done

cd ..

# 部署到Kubernetes
echo "🚀 开始部署到Kubernetes..."

if [[ "$DEPLOY_ALL" == "true" ]]; then
  echo "🚀 --all 模式: 部署所有服务..."
  
  # 应用所有Kubernetes资源
  echo "📦 应用中间件..."
  kubectl apply -f k8s/middleware/ -n "$NAMESPACE"
  
  echo "📦 应用配置..."
  kubectl apply -f k8s/configs/ -n "$NAMESPACE"
  
  echo "📦 应用所有部署..."
  kubectl apply -f k8s/deployments/ -n "$NAMESPACE"
  
  # 等待部署完成
  echo "⏳ 等待部署完成..."
  kubectl wait --for=condition=available --timeout=300s deployment --all -n "$NAMESPACE"
  
else
  echo "🚀 精确模式: 根据BUILD_PROJECTS配置部署指定服务..."
  
  # 确保基础设施已部署
  kubectl apply -f k8s/middleware/ -n "$NAMESPACE"
  kubectl apply -f k8s/configs/ -n "$NAMESPACE"
  
  # 部署指定的服务
  for project in "${BUILD_PROJECTS[@]}"; do
    deployment_file="k8s/deployments/${project}.yaml"
    if [[ -f "$deployment_file" ]]; then
      echo "📦 部署服务: $project"
      kubectl apply -f "$deployment_file" -n "$NAMESPACE"
    else
      echo "⚠️  警告: 部署文件不存在: $deployment_file"
    fi
  done
  
  # 等待指定服务部署完成
  for project in "${BUILD_PROJECTS[@]}"; do
    deployment_name=$(get_k8s_deployment_name "$project")
    if kubectl get deployment "$deployment_name" -n "$NAMESPACE" >/dev/null 2>&1; then
      echo "⏳ 等待部署完成: $deployment_name"
      kubectl wait --for=condition=available --timeout=300s deployment "$deployment_name" -n "$NAMESPACE"
    fi
  done
fi

# 检查部署状态
echo "📊 检查部署状态:"
kubectl get pods -n "$NAMESPACE" -o wide
echo ""
kubectl get services -n "$NAMESPACE"
echo ""
kubectl get ingress -n "$NAMESPACE"

echo "✅ Kubernetes部署完成。"

# 获取Kubernetes部署名称的函数
get_k8s_deployment_name() {
  local proj=$1
  echo "$proj"  # 在K8s中，部署名称通常与项目名称相同
}
