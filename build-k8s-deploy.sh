#!/bin/bash

set -e

# 解析命令行参数
DEPLOY_ALL=false
RESTART_ONLY=false
SHOW_HELP=false
NAMESPACE="trinax-system"
KUBECONFIG_PATH=""
BUILD_ONLY=false
ENVIRONMENT="prod"

while [[ $# -gt 0 ]]; do
  case $1 in
    --all)
      DEPLOY_ALL=true
      shift
      ;;
    --restart-only)
      RESTART_ONLY=true
      shift
      ;;
    --build-only)
      BUILD_ONLY=true
      shift
      ;;
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --kubeconfig)
      KUBECONFIG_PATH="$2"
      shift 2
      ;;
    --help|-h)
      SHOW_HELP=true
      shift
      ;;
    *)
      echo "未知参数: $1"
      echo "使用 --help 查看帮助信息"
      exit 1
      ;;
  esac
done

# 显示帮助信息
if [[ "$SHOW_HELP" == "true" ]]; then
  echo "DTT-Trinasolar项目Kubernetes自动化构建部署脚本"
  echo ""
  echo "使用方法:"
  echo "  $0                          # 根据BUILD_PROJECTS配置构建和部署指定服务"
  echo "  $0 --all                    # 部署所有项目并重启所有服务"
  echo "  $0 --restart-only           # 仅重启服务，不重新构建镜像"
  echo "  $0 --build-only             # 仅构建镜像，不部署到K8s"
  echo "  $0 --namespace <ns>         # 指定Kubernetes命名空间"
  echo "  $0 --kubeconfig <path>      # 指定kubeconfig文件路径"
  echo "  $0 --help                   # 显示此帮助信息"
  echo ""
  echo "配置文件: conf/drb-deploy.conf"
  echo "Kubernetes配置: k8s/"
  exit 0
fi

# 载入配置
source ./conf/drb-deploy.conf

CONF_DIR="$BASE_DIR/conf"
DOCKER_COMPOSE_DIR="$CONF_DIR/drb-docker-compose"
DOCKERFILE="$BASE_DIR/Dockerfile"
PROJECT_DIR="$BASE_DIR/dtt-trinasolar"

# 设置kubeconfig
if [[ -n "$KUBECONFIG_PATH" ]]; then
  export KUBECONFIG="$KUBECONFIG_PATH"
fi

# 如果不是仅构建模式，检查kubectl连接
if [[ "$BUILD_ONLY" != "true" ]]; then
  echo "🔍 检查Kubernetes集群连接..."
  if ! kubectl cluster-info >/dev/null 2>&1; then
    echo "❌ 无法连接到Kubernetes集群，请检查kubeconfig配置"
    exit 1
  fi

  echo "✅ Kubernetes集群连接正常"
  echo "📍 当前上下文: $(kubectl config current-context)"
  echo "📍 目标命名空间: $NAMESPACE"

  # 确保命名空间存在
  echo "🔧 确保命名空间存在..."
  kubectl apply -f k8s/base/namespace.yaml
fi

# 如果是仅重启模式，跳过构建步骤
if [[ "$RESTART_ONLY" == "true" ]]; then
  echo "🔄 仅重启模式: 跳过代码拉取和镜像构建，直接重启服务..."

  if [[ "$DEPLOY_ALL" == "true" ]]; then
    echo "🚀 重启所有服务..."
    kubectl rollout restart deployment -n "$NAMESPACE"
  else
    echo "🚀 重启指定服务..."
    for project in "${BUILD_PROJECTS[@]}"; do
      deployment_name="$project"
      if kubectl get deployment "$deployment_name" -n "$NAMESPACE" >/dev/null 2>&1; then
        echo "🔄 重启部署: $deployment_name"
        kubectl rollout restart deployment "$deployment_name" -n "$NAMESPACE"
      else
        echo "⚠️  警告: 部署 $deployment_name 不存在"
      fi
    done
  fi

  echo "✅ 重启完成。"
  exit 0
fi

# 构建镜像
echo "📦 开始构建Docker镜像..."

# 清理工作目录
echo "📌 清理工作目录中非 conf/、build-deploy.sh、Dockerfile..."
find "$BASE_DIR" -mindepth 1 -maxdepth 1 \
  ! -name "conf" \
  ! -name "build-deploy.sh" \
  ! -name "build-all.sh" \
  ! -name "build-frontend.sh" \
  ! -name "build-k8s-deploy.sh" \
  ! -name "Dockerfile" \
  ! -name "Dockerfile-Frontend" \
  ! -name ".gitignore" \
  ! -name ".git" \
  ! -name "README.md" \
  ! -name "k8s" \
  ! -name "tmp_npm_cache" \
  ! -name "tmp-mvn-repo" \
  -exec rm -rf {} +

# 📦 Step 1: 克隆所有 BUILD_PROJECTS 对应的仓库
cd "$BASE_DIR"
repo_url="${GIT_REPO}"
echo "📦 克隆仓库: $repo_url"
git clone "$repo_url"

if [ ! -d "$PROJECT_DIR/.git" ]; then
  echo "❌ 克隆失败或非 git 仓库目录: $PROJECT_DIR"
  exit 1
fi

echo "🔀 切换 $PROJECT_DIR 分支: $BRANCH"
git -C "$PROJECT_DIR" checkout "$BRANCH"
git -C "$PROJECT_DIR" pull

# 📦 Step 2: 构建/打包/构建镜像
# 后端项目打包流程
echo "📦 Maven 构建公共包..."
echo "project dir $PROJECT_DIR"
cd "$PROJECT_DIR"
ls

# 2.1 打包 dtt-component
if [ -d "dtt-component" ]; then
  echo "➡️ 处理公共依赖目录: dtt-component"
  cd dtt-component
  mvn clean install -T 1C -Dmaven.test.skip=true -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.2 打包 trinax-shared
if [ -d "trinax-shared" ]; then
  echo "➡️ 处理共享依赖目录: trinax-shared"
  cd trinax-shared
  mvn clean install -T 1C -Dmaven.test.skip=true -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.3 打包 trinax-auth-center-core
if [ -d "trinax-auth-center" ]; then
  echo "➡️ 处理共享依赖目录: trinax-auth-center"
  cd trinax-auth-center
  mvn clean install -T 1C -Dmaven.test.skip=true -pl trinax-auth-center-core -am -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.4 打包 trinax-behavior-log
if [ -d "trinax-report" ]; then
  echo "➡️ 处理特别依赖目录: trinax-behavior-log"
  cd trinax-report
  mvn clean install -T 1C -Dmaven.test.skip=true -pl trinax-behavior-log -am -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.5 打包 trinax-integration-api(由于trinax-contract-api依赖)
if [ -d "trinax-integration" ]; then
  echo "➡️ 处理共享依赖目录: trinax-integration-api"
  cd trinax-integration
  mvn clean install -T 1C -Dmaven.test.skip=true -pl trinax-integration-api -am -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.6 打包 trinax-masterdata-api(由于trinax-contract-api依赖)
if [ -d "trinax-masterdata" ]; then
  echo "➡️ 处理共享依赖目录: trinax-masterdata-api"
  cd trinax-masterdata
  mvn clean install -T 1C -Dmaven.test.skip=true -pl trinax-masterdata-api -am -s "$MAVEN_SETTINGS"
  cd ..
fi

# 2.7 打包 所有的api
# 获取当前目录下所有文件夹名
for dir in */; do
    # 去除末尾的斜杠
    dir=${dir%/}

    # 拼接-api后缀
    api_dir="${dir}-api"
    cd $dir
    echo "尝试处理 $api_dir 目录"

    # 检查拼接后的目录是否存在
    if [ -d "$api_dir" ]; then
      echo "➡️ 处理业务包API目录: $api_dir"
      pwd
      echo "mvn clean install -T 1C -Dmaven.test.skip=true -pl $api_dir -s $MAVEN_SETTINGS"
      mvn clean install -T 1C -Dmaven.test.skip=true -pl "$api_dir" -am -s "$MAVEN_SETTINGS"
    else
      echo "❌ $api_dir 目录不存在，跳过构建"
    fi
    ## 回到上级目录
    cd ..
done

for proj in "${BUILD_PROJECTS[@]}"; do
  echo "📦 开始构建项目: $proj"

  # 推导jar包路径
  biz_dir=$(find . -name $proj)

  # 推导模块名称
  module_dir=$(echo $biz_dir | cut -d'/' -f2)
  echo "📦 module_dir名称: $module_dir"
  cd $module_dir
  mvn clean install -T 1C -Dmaven.test.skip=true -s "$MAVEN_SETTINGS"
  cd ..

  echo "📦 jar包目录: $biz_dir"
  jar_path="${biz_dir}/target/$proj.jar"

  if [ ! -f "$jar_path" ]; then
    echo "❌ 没找到 $jar_path，跳过镜像构建"
    continue
  fi

  # 获取项目对应的端口号（优先从配置文件读取）
  port=$(get_port_mapping "$proj")
  memory=$(get_memory_mapping "$proj")

  echo "🐳 Docker build 镜像 $proj, jar 路径 $jar_path, 端口 $port, 内存限制 $memory"
  docker build -f "$DOCKERFILE" \
    --build-arg JAR_PATH="$jar_path" \
    --build-arg EXPOSE_PORT="$port" \
    --build-arg MEMORY_LIMIT="$memory" \
    -t "$proj:latest" .

  cd "$PROJECT_DIR"
done

# 镜像打包完成后回到上级目录
cd ..

# 如果是仅构建模式，到此结束
if [[ "$BUILD_ONLY" == "true" ]]; then
  echo "✅ 镜像构建完成。"
  echo "📊 构建的镜像列表:"
  for proj in "${BUILD_PROJECTS[@]}"; do
    echo "  - $proj:latest"
  done
  exit 0
fi

# 🚀 Step 3: 根据配置精确控制Kubernetes部署
echo "🚀 开始部署到Kubernetes..."

if [[ "$DEPLOY_ALL" == "true" ]]; then
  echo "🚀 --all 模式: 重新部署所有Kubernetes服务..."

  echo "📦 应用中间件..."
  kubectl apply -f k8s/middleware/ -n "$NAMESPACE"

  echo "📦 应用配置..."
  kubectl apply -f k8s/configs/ -n "$NAMESPACE"

  echo "📦 应用所有部署..."
  kubectl apply -f k8s/deployments/ -n "$NAMESPACE"

  # 等待中间件服务启动
  echo "⏳ 等待中间件服务启动..."
  kubectl wait --for=condition=available --timeout=300s deployment/mysql -n "$NAMESPACE" || true
  kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE" || true

  # 等待应用服务启动
  echo "⏳ 等待应用服务启动..."
  kubectl wait --for=condition=available --timeout=600s deployment --all -n "$NAMESPACE" || true

else
  echo "🚀 精确模式: 根据BUILD_PROJECTS配置控制指定服务..."

  # 确保基础设施已部署
  echo "📦 确保中间件已部署..."
  kubectl apply -f k8s/middleware/ -n "$NAMESPACE"
  kubectl apply -f k8s/configs/ -n "$NAMESPACE"

  # 等待中间件就绪
  echo "⏳ 等待中间件就绪..."
  kubectl wait --for=condition=available --timeout=300s deployment/mysql -n "$NAMESPACE" || true
  kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE" || true

  # 构建需要操作的服务列表
  DEPLOY_SERVICES=()
  for project in "${BUILD_PROJECTS[@]}"; do
    deployment_file="k8s/deployments/${project}.yaml"
    if [[ -f "$deployment_file" ]]; then
      DEPLOY_SERVICES+=("$project")
      echo "📦 项目 $project -> 部署文件 $deployment_file"
    else
      echo "⚠️  警告: 项目 $project 没有对应的部署文件"
    fi
  done

  if [[ ${#DEPLOY_SERVICES[@]} -eq 0 ]]; then
    echo "❌ 错误: 没有找到任何有效的部署文件"
    exit 1
  fi

  echo "📦 部署指定的服务: ${DEPLOY_SERVICES[*]}"
  for project in "${DEPLOY_SERVICES[@]}"; do
    deployment_file="k8s/deployments/${project}.yaml"
    echo "📦 部署服务: $project"
    kubectl apply -f "$deployment_file" -n "$NAMESPACE"
  done

  # 等待指定服务部署完成
  echo "⏳ 等待指定服务部署完成..."
  for project in "${DEPLOY_SERVICES[@]}"; do
    echo "⏳ 等待部署完成: $project"
    kubectl wait --for=condition=available --timeout=300s deployment "$project" -n "$NAMESPACE" || true
  done
fi

# 检查部署状态
echo "📊 检查部署状态:"
kubectl get pods -n "$NAMESPACE" -o wide
echo ""
echo "📊 检查服务状态:"
kubectl get services -n "$NAMESPACE"
echo ""
echo "📊 检查Ingress状态:"
kubectl get ingress -n "$NAMESPACE"

# 检查具体服务状态
echo ""
echo "📊 检查各服务详细状态:"
if [[ "$DEPLOY_ALL" == "true" ]]; then
  # 检查所有服务
  for project in "${BUILD_PROJECTS[@]}"; do
    status=$(kubectl get deployment "$project" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}/{.status.replicas}' 2>/dev/null || echo "未找到")
    if [[ "$status" != "未找到" ]]; then
      echo "  ✅ $project: $status 副本就绪"
    else
      echo "  ❌ $project: 部署未找到"
    fi
  done
else
  # 检查指定服务
  for project in "${DEPLOY_SERVICES[@]}"; do
    status=$(kubectl get deployment "$project" -n "$NAMESPACE" -o jsonpath='{.status.readyReplicas}/{.status.replicas}' 2>/dev/null || echo "未找到")
    if [[ "$status" != "未找到" ]]; then
      echo "  ✅ $project: $status 副本就绪"
    else
      echo "  ❌ $project: 部署未找到"
    fi
  done
fi

echo ""
echo "✅ Kubernetes部署完成。"
echo ""
echo "🔗 访问信息:"
echo "  - 获取Ingress IP: kubectl get ingress -n $NAMESPACE"
echo "  - 查看Pod日志: kubectl logs -f deployment/<service-name> -n $NAMESPACE"
echo "  - 查看服务状态: kubectl get pods -n $NAMESPACE"
