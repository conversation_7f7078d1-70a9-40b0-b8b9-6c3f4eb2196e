#!/bin/bash

set -e

# 简化的Kubernetes部署脚本
# 用于快速部署已构建好的镜像到Kubernetes

NAMESPACE="trinax-system"
KUBECONFIG_PATH=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --kubeconfig)
      KUBECONFIG_PATH="$2"
      shift 2
      ;;
    --help|-h)
      echo "简化的Kubernetes部署脚本"
      echo ""
      echo "使用方法:"
      echo "  $0                          # 部署到默认命名空间 trinax-system"
      echo "  $0 --namespace <ns>         # 指定Kubernetes命名空间"
      echo "  $0 --kubeconfig <path>      # 指定kubeconfig文件路径"
      echo "  $0 --help                   # 显示此帮助信息"
      echo ""
      echo "前提条件: Docker镜像已经构建完成"
      exit 0
      ;;
    *)
      echo "未知参数: $1"
      echo "使用 --help 查看帮助信息"
      exit 1
      ;;
  esac
done

# 设置kubeconfig
if [[ -n "$KUBECONFIG_PATH" ]]; then
  export KUBECONFIG="$KUBECONFIG_PATH"
fi

# 检查kubectl连接
echo "🔍 检查Kubernetes集群连接..."
if ! kubectl cluster-info >/dev/null 2>&1; then
  echo "❌ 无法连接到Kubernetes集群，请检查kubeconfig配置"
  exit 1
fi

echo "✅ Kubernetes集群连接正常"
echo "📍 当前上下文: $(kubectl config current-context)"
echo "📍 目标命名空间: $NAMESPACE"

# 确保命名空间存在
echo "🔧 创建命名空间..."
kubectl apply -f k8s/base/namespace.yaml

# 部署中间件
echo "📦 部署中间件服务..."
kubectl apply -f k8s/middleware/ -n "$NAMESPACE"

# 等待中间件启动
echo "⏳ 等待中间件服务启动..."
kubectl wait --for=condition=available --timeout=300s deployment/mysql -n "$NAMESPACE" || true
kubectl wait --for=condition=available --timeout=300s deployment/redis -n "$NAMESPACE" || true

# 部署配置
echo "📦 部署配置文件..."
kubectl apply -f k8s/configs/ -n "$NAMESPACE"

# 部署应用服务
echo "📦 部署应用服务..."
kubectl apply -f k8s/deployments/ -n "$NAMESPACE"

# 等待应用服务启动
echo "⏳ 等待应用服务启动..."
kubectl wait --for=condition=available --timeout=600s deployment --all -n "$NAMESPACE" || true

# 检查部署状态
echo ""
echo "📊 检查部署状态:"
kubectl get pods -n "$NAMESPACE" -o wide

echo ""
echo "📊 检查服务状态:"
kubectl get services -n "$NAMESPACE"

echo ""
echo "📊 检查Ingress状态:"
kubectl get ingress -n "$NAMESPACE"

echo ""
echo "✅ Kubernetes部署完成！"
echo ""
echo "🔗 有用的命令:"
echo "  - 查看所有Pod状态: kubectl get pods -n $NAMESPACE"
echo "  - 查看服务日志: kubectl logs -f deployment/<service-name> -n $NAMESPACE"
echo "  - 获取Ingress访问地址: kubectl get ingress -n $NAMESPACE"
echo "  - 进入Pod调试: kubectl exec -it deployment/<service-name> -n $NAMESPACE -- /bin/bash"
