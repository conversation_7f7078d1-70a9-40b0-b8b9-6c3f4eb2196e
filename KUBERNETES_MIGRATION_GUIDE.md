# Trinax项目Kubernetes迁移指南

## 概述

本文档详细说明了从Docker Compose部署迁移到Kubernetes部署的完整过程和注意事项。

## 迁移前准备

### 1. 环境要求

- Kubernetes集群 (v1.20+)
- kubectl命令行工具
- Docker镜像仓库（Harbor/Docker Hub等）
- Helm 3.x（可选，用于包管理）
- Ingress Controller（如nginx-ingress）

### 2. 集群资源规划

```yaml
# 最小资源要求
CPU: 8 cores
Memory: 16GB
Storage: 100GB
Nodes: 3个节点（生产环境建议）

# 推荐资源配置
CPU: 16 cores
Memory: 32GB
Storage: 200GB
Nodes: 5个节点
```

## 迁移步骤

### 第一阶段：基础设施准备

1. **创建命名空间**
```bash
kubectl apply -f k8s/base/namespace.yaml
```

2. **部署中间件**
```bash
# 部署MySQL
kubectl apply -f k8s/middleware/mysql.yaml

# 部署Redis
kubectl apply -f k8s/middleware/redis.yaml

# 等待中间件就绪
kubectl wait --for=condition=available --timeout=300s deployment/mysql -n trinax-system
kubectl wait --for=condition=available --timeout=300s deployment/redis -n trinax-system
```

3. **配置存储类**
```bash
# 检查可用存储类
kubectl get storageclass

# 如果需要，创建自定义存储类
kubectl apply -f k8s/storage/storageclass.yaml
```

### 第二阶段：应用迁移

1. **构建和推送镜像**
```bash
# 使用新的K8s构建脚本
chmod +x build-k8s-deploy.sh
./build-k8s-deploy.sh --all
```

2. **部署配置**
```bash
# 应用ConfigMaps和Secrets
kubectl apply -f k8s/configs/
```

3. **部署应用服务**
```bash
# 分批部署，避免资源争抢
kubectl apply -f k8s/deployments/trinax-auth-center-boot.yaml
kubectl apply -f k8s/deployments/dtt-gateway.yaml
# ... 其他服务
```

### 第三阶段：网络和访问配置

1. **配置Ingress**
```bash
# 确保Ingress Controller已安装
kubectl get pods -n ingress-nginx

# 应用Ingress规则
kubectl apply -f k8s/deployments/dtt-gateway.yaml
```

2. **DNS配置**
```bash
# 获取Ingress IP
kubectl get ingress -n trinax-system

# 配置域名解析
# trinax.local -> <INGRESS_IP>
```

### 第四阶段：监控和日志

1. **部署Prometheus监控**
```bash
# 如果使用Prometheus Operator
kubectl apply -f k8s/monitoring/prometheus-config.yaml
```

2. **配置日志收集**
```bash
# 部署Fluentd或Filebeat
kubectl apply -f k8s/logging/
```

## 配置差异对比

### Docker Compose vs Kubernetes

| 功能 | Docker Compose | Kubernetes |
|------|----------------|------------|
| 服务发现 | 容器名称 | Service DNS |
| 负载均衡 | 内置 | Service + Ingress |
| 配置管理 | 环境变量 | ConfigMap + Secret |
| 存储 | 本地卷 | PV + PVC |
| 网络 | Bridge网络 | CNI网络 |
| 扩缩容 | 手动 | HPA自动 |

### 配置文件映射

```yaml
# Docker Compose
services:
  trinax-auth-center-boot:
    image: trinax-auth-center-boot:latest
    ports:
      - "7020:7020"
    environment:
      - SPRING_PROFILES_ACTIVE=local

# Kubernetes
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-auth-center-boot
spec:
  template:
    spec:
      containers:
      - name: trinax-auth-center-boot
        image: trinax-auth-center-boot:latest
        ports:
        - containerPort: 7020
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
```

## 注意事项

### 1. 应用配置调整

- **Spring Profile**: 从`local`改为`k8s`
- **服务发现**: 使用Kubernetes Service DNS
- **数据库连接**: 使用Service名称而非IP
- **配置外部化**: 使用ConfigMap和Secret

### 2. 网络配置

```yaml
# 原Docker Compose网络
trinax-auth-center-boot:7020

# Kubernetes Service网络
trinax-auth-center-boot-service.trinax-system.svc.cluster.local:7020
```

### 3. 存储迁移

- 数据库数据需要导出导入
- 文件存储需要迁移到PV
- 配置文件转换为ConfigMap

### 4. 监控和日志

- 应用需要暴露Prometheus metrics端点
- 日志输出到stdout/stderr
- 健康检查端点配置

## 回滚计划

如果迁移过程中出现问题，可以快速回滚到Docker Compose：

1. **停止Kubernetes部署**
```bash
kubectl delete namespace trinax-system
```

2. **恢复Docker Compose**
```bash
cd conf/drb-docker-compose
docker-compose up -d
```

3. **数据恢复**
```bash
# 从备份恢复数据库
mysql < backup.sql
```

## 性能优化建议

### 1. 资源限制
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "1Gi"
    cpu: "1000m"
```

### 2. 水平扩缩容
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: trinax-auth-center-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: trinax-auth-center-boot
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### 3. 节点亲和性
```yaml
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: node-type
          operator: In
          values:
          - compute
```

## 故障排查

### 常见问题

1. **Pod启动失败**
```bash
kubectl describe pod <pod-name> -n trinax-system
kubectl logs <pod-name> -n trinax-system
```

2. **服务无法访问**
```bash
kubectl get svc -n trinax-system
kubectl get endpoints -n trinax-system
```

3. **配置问题**
```bash
kubectl get configmap -n trinax-system
kubectl describe configmap <configmap-name> -n trinax-system
```

## 总结

Kubernetes迁移是一个复杂的过程，需要仔细规划和测试。建议：

1. 先在测试环境完整验证
2. 分阶段进行生产环境迁移
3. 做好数据备份和回滚准备
4. 监控迁移过程中的性能指标
5. 团队培训Kubernetes运维知识
