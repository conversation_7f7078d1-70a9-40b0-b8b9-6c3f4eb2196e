#!/bin/bash

set -e

# Kubernetes资源清理脚本

NAMESPACE="trinax-system"
KUBECONFIG_PATH=""
FORCE_DELETE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --kubeconfig)
      KUBECONFIG_PATH="$2"
      shift 2
      ;;
    --force)
      FORCE_DELETE=true
      shift
      ;;
    --help|-h)
      echo "Kubernetes资源清理脚本"
      echo ""
      echo "使用方法:"
      echo "  $0                          # 清理默认命名空间 trinax-system"
      echo "  $0 --namespace <ns>         # 指定要清理的Kubernetes命名空间"
      echo "  $0 --kubeconfig <path>      # 指定kubeconfig文件路径"
      echo "  $0 --force                  # 强制删除，不询问确认"
      echo "  $0 --help                   # 显示此帮助信息"
      echo ""
      echo "⚠️  警告: 此操作将删除指定命名空间中的所有资源！"
      exit 0
      ;;
    *)
      echo "未知参数: $1"
      echo "使用 --help 查看帮助信息"
      exit 1
      ;;
  esac
done

# 设置kubeconfig
if [[ -n "$KUBECONFIG_PATH" ]]; then
  export KUBECONFIG="$KUBECONFIG_PATH"
fi

# 检查kubectl连接
echo "🔍 检查Kubernetes集群连接..."
if ! kubectl cluster-info >/dev/null 2>&1; then
  echo "❌ 无法连接到Kubernetes集群，请检查kubeconfig配置"
  exit 1
fi

echo "✅ Kubernetes集群连接正常"
echo "📍 当前上下文: $(kubectl config current-context)"
echo "📍 目标命名空间: $NAMESPACE"

# 检查命名空间是否存在
if ! kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
  echo "⚠️  命名空间 $NAMESPACE 不存在，无需清理"
  exit 0
fi

# 显示将要删除的资源
echo ""
echo "📊 当前命名空间中的资源:"
kubectl get all -n "$NAMESPACE" 2>/dev/null || echo "  (无资源)"

echo ""
echo "📊 PVC资源:"
kubectl get pvc -n "$NAMESPACE" 2>/dev/null || echo "  (无PVC)"

echo ""
echo "📊 ConfigMap和Secret:"
kubectl get configmap,secret -n "$NAMESPACE" 2>/dev/null || echo "  (无ConfigMap/Secret)"

# 确认删除
if [[ "$FORCE_DELETE" != "true" ]]; then
  echo ""
  echo "⚠️  警告: 即将删除命名空间 '$NAMESPACE' 中的所有资源！"
  echo "这包括:"
  echo "  - 所有Deployment、Service、Pod"
  echo "  - 所有PVC和存储的数据"
  echo "  - 所有ConfigMap和Secret"
  echo "  - Ingress规则"
  echo ""
  read -p "确认要继续吗？(输入 'yes' 确认): " confirm
  
  if [[ "$confirm" != "yes" ]]; then
    echo "❌ 操作已取消"
    exit 0
  fi
fi

echo ""
echo "🗑️  开始清理资源..."

# 删除应用部署
echo "🗑️  删除应用部署..."
kubectl delete -f k8s/deployments/ -n "$NAMESPACE" --ignore-not-found=true

# 删除配置
echo "🗑️  删除配置..."
kubectl delete -f k8s/configs/ -n "$NAMESPACE" --ignore-not-found=true

# 删除中间件
echo "🗑️  删除中间件..."
kubectl delete -f k8s/middleware/ -n "$NAMESPACE" --ignore-not-found=true

# 等待Pod终止
echo "⏳ 等待Pod终止..."
kubectl wait --for=delete pod --all -n "$NAMESPACE" --timeout=300s || true

# 删除PVC（如果存在）
echo "🗑️  删除PVC..."
kubectl delete pvc --all -n "$NAMESPACE" --ignore-not-found=true

# 删除命名空间
echo "🗑️  删除命名空间..."
kubectl delete namespace "$NAMESPACE" --ignore-not-found=true

echo ""
echo "✅ 清理完成！"
echo ""
echo "📊 验证清理结果:"
if kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
  echo "⚠️  命名空间仍在终止中..."
  echo "可以使用以下命令检查状态:"
  echo "  kubectl get namespace $NAMESPACE"
else
  echo "✅ 命名空间已完全删除"
fi
