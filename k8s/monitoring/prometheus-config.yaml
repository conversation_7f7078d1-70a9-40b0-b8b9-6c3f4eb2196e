apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: trinax-system
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "alert_rules.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - trinax-system
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
      
      - job_name: 'trinax-services'
        static_configs:
          - targets:
            - 'trinax-auth-center-boot-service:7020'
            - 'trinax-basic-boot-service:7021'
            - 'trinax-contract-boot-service:7022'
            - 'trinax-delivery-boot-service:7023'
            - 'trinax-filebff-service:7024'
            - 'trinax-integration-boot-service:7025'
            - 'trinax-masterdata-boot-service:7026'
            - 'trinax-partner-boot-service:7027'
            - 'trinax-partnerbff-service:7028'
            - 'trinax-report-boot-service:7029'
            - 'trinax-sharedapi-service:7030'
            - 'trinax-staffbff-service:7031'
            - 'trinax-user-boot-service:7032'
            - 'trinax-filegateway-service:8001'
            - 'dtt-gateway-service:80'
        metrics_path: '/actuator/prometheus'
        scrape_interval: 30s
        scrape_timeout: 10s
        
  alert_rules.yml: |
    groups:
      - name: trinax-alerts
        rules:
          - alert: ServiceDown
            expr: up == 0
            for: 1m
            labels:
              severity: critical
            annotations:
              summary: "Service {{ $labels.instance }} is down"
              description: "{{ $labels.instance }} has been down for more than 1 minute."
          
          - alert: HighMemoryUsage
            expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage on {{ $labels.pod }}"
              description: "Memory usage is above 80% for {{ $labels.pod }}"
          
          - alert: HighCPUUsage
            expr: (rate(container_cpu_usage_seconds_total[5m]) * 100) > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage on {{ $labels.pod }}"
              description: "CPU usage is above 80% for {{ $labels.pod }}"
          
          - alert: PodCrashLooping
            expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Pod {{ $labels.pod }} is crash looping"
              description: "Pod {{ $labels.pod }} has restarted {{ $value }} times in the last 15 minutes"
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: trinax-services-monitor
  namespace: trinax-system
  labels:
    app: trinax-monitor
spec:
  selector:
    matchLabels:
      component: backend
  endpoints:
  - port: http
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
