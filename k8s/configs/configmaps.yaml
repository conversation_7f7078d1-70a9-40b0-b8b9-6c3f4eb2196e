apiVersion: v1
kind: ConfigMap
metadata:
  name: trinax-auth-center-config
  namespace: trinax-system
data:
  application-k8s.yml: |
    server:
      port: 7020
    spring:
      datasource:
        url: ***************************************************************************************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      redis:
        host: redis-service
        port: 6379
        password: ${REDIS_PASSWORD}
    eureka:
      client:
        service-url:
          defaultZone: http://eureka-service:8761/eureka/
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics
      endpoint:
        health:
          show-details: always
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: dtt-gateway-config
  namespace: trinax-system
data:
  application-k8s.yml: |
    server:
      port: 80
    spring:
      cloud:
        gateway:
          routes:
            - id: auth-center
              uri: http://trinax-auth-center-boot-service:7020
              predicates:
                - Path=/auth/**
            - id: basic-service
              uri: http://trinax-basic-boot-service:7021
              predicates:
                - Path=/basic/**
            - id: contract-service
              uri: http://trinax-contract-boot-service:7022
              predicates:
                - Path=/contract/**
            - id: delivery-service
              uri: http://trinax-delivery-boot-service:7023
              predicates:
                - Path=/delivery/**
            - id: file-bff
              uri: http://trinax-filebff-service:7024
              predicates:
                - Path=/file/**
            - id: integration-service
              uri: http://trinax-integration-boot-service:7025
              predicates:
                - Path=/integration/**
            - id: masterdata-service
              uri: http://trinax-masterdata-boot-service:7026
              predicates:
                - Path=/masterdata/**
            - id: partner-service
              uri: http://trinax-partner-boot-service:7027
              predicates:
                - Path=/partner/**
            - id: partner-bff
              uri: http://trinax-partnerbff-service:7028
              predicates:
                - Path=/partnerbff/**
            - id: report-service
              uri: http://trinax-report-boot-service:7029
              predicates:
                - Path=/report/**
            - id: shared-api
              uri: http://trinax-sharedapi-service:7030
              predicates:
                - Path=/shared/**
            - id: staff-bff
              uri: http://trinax-staffbff-service:7031
              predicates:
                - Path=/staff/**
            - id: user-service
              uri: http://trinax-user-boot-service:7032
              predicates:
                - Path=/user/**
            - id: file-gateway
              uri: http://trinax-filegateway-service:8001
              predicates:
                - Path=/filegateway/**
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,gateway
---
apiVersion: v1
kind: Secret
metadata:
  name: trinax-secrets
  namespace: trinax-system
type: Opaque
data:
  # Base64编码的敏感信息
  DB_USERNAME: cm9vdA==  # root
  DB_PASSWORD: cGFzc3dvcmQ=  # password
  REDIS_PASSWORD: cmVkaXNwYXNz  # redispass
  JWT_SECRET: bXlqd3RzZWNyZXRrZXk=  # myjwtsecretkey
