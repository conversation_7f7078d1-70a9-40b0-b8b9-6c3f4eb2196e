# Kubernetes部署配置说明

## 目录结构

```
k8s/
├── kustomization.yaml              # 基础配置，包含所有通用资源
├── base/                          # 基础资源
│   └── namespace.yaml
├── configs/                       # 配置文件
│   └── configmaps.yaml
├── middleware/                    # 中间件
│   ├── mysql.yaml
│   └── redis.yaml
├── deployments/                   # 应用部署文件
│   ├── trinax-auth-center-boot.yaml
│   ├── trinax-basic-boot.yaml
│   ├── ...
│   └── dtt-gateway.yaml
└── environments/                  # 环境特定配置
    ├── dev/                      # 开发环境
    │   ├── kustomization.yaml
    │   └── resource-limits-dev.yaml
    ├── test/                     # 测试环境
    │   └── kustomization.yaml
    └── prod/                     # 生产环境
        ├── kustomization.yaml
        └── resource-limits-prod.yaml
```

## 设计理念

### 1. 基础配置 (k8s/kustomization.yaml)
- 包含所有通用的Kubernetes资源
- 作为base层，供不同环境继承
- 不包含环境特定的配置

### 2. 环境特定配置 (environments/)
- **开发环境 (dev/)**：
  - 命名空间：`trinax-dev`
  - 副本数：1个
  - 资源限制：较低
  - 镜像标签：`dev-latest`
  - 日志级别：DEBUG

- **测试环境 (test/)**：
  - 命名空间：`trinax-test`
  - 副本数：1个
  - 资源限制：中等
  - 镜像标签：`test-latest`
  - 日志级别：INFO

- **生产环境 (prod/)**：
  - 命名空间：`trinax-system`
  - 副本数：2个
  - 资源限制：较高
  - 镜像标签：`latest`
  - 日志级别：INFO

## 使用方法

### 1. 部署到不同环境

```bash
# 部署到开发环境
kubectl apply -k k8s/environments/dev/

# 部署到测试环境
kubectl apply -k k8s/environments/test/

# 部署到生产环境
kubectl apply -k k8s/environments/prod/
```

### 2. 使用构建脚本

```bash
# 构建并部署到开发环境
./build-k8s-deploy.sh --env dev --all

# 构建并部署到生产环境
./build-k8s-deploy.sh --env prod --all

# 仅构建镜像
./build-k8s-deploy.sh --build-only

# 仅重启服务
./build-k8s-deploy.sh --env dev --restart-only
```

### 3. 查看不同环境的配置差异

```bash
# 查看开发环境的最终配置
kubectl kustomize k8s/environments/dev/

# 查看生产环境的最终配置
kubectl kustomize k8s/environments/prod/
```

## 配置差异对比

| 配置项 | 开发环境 | 测试环境 | 生产环境 |
|--------|----------|----------|----------|
| 命名空间 | trinax-dev | trinax-test | trinax-system |
| 副本数 | 1 | 1 | 2 |
| 内存请求 | 256Mi | 512Mi | 1Gi |
| 内存限制 | 512Mi | 1Gi | 2Gi |
| CPU请求 | 100m | 250m | 500m |
| CPU限制 | 500m | 1000m | 1500m |
| 镜像标签 | dev-latest | test-latest | latest |
| 日志级别 | DEBUG | INFO | INFO |
| 名称前缀 | dev- | test- | 无 |

## 自定义配置

### 1. 添加新环境
1. 在 `environments/` 下创建新目录
2. 复制现有环境的 `kustomization.yaml`
3. 修改相应的配置参数

### 2. 修改资源限制
编辑对应环境的 `resource-limits-*.yaml` 文件

### 3. 添加环境特定的补丁
在环境目录下创建新的补丁文件，并在 `kustomization.yaml` 中引用

## 故障排查

### 1. 检查配置是否正确
```bash
kubectl kustomize k8s/environments/dev/ | kubectl apply --dry-run=client -f -
```

### 2. 查看实际应用的资源
```bash
kubectl get all -n trinax-dev
```

### 3. 检查配置差异
```bash
kubectl diff -k k8s/environments/dev/
```
