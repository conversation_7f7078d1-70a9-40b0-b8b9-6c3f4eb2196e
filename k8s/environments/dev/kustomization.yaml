apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: trinax-dev

resources:
  - ../../base

namePrefix: dev-

commonLabels:
  environment: development

patchesStrategicMerge:
  - deployment-patches.yaml
  - service-patches.yaml

configMapGenerator:
  - name: trinax-env-config
    behavior: merge
    literals:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - SPRING_PROFILES_ACTIVE=dev

replicas:
  - name: trinax-auth-center-boot
    count: 1
  - name: trinax-basic-boot
    count: 1
  - name: dtt-gateway
    count: 1

images:
  - name: trinax-auth-center-boot
    newTag: dev-latest
  - name: trinax-basic-boot
    newTag: dev-latest
  - name: dtt-gateway
    newTag: dev-latest
