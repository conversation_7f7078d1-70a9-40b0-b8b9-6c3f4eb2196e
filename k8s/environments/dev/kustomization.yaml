apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: trinax-dev

resources:
  - ../../

namePrefix: dev-

commonLabels:
  environment: development

images:
  - name: trinax-auth-center-boot
    newTag: dev-latest
  - name: trinax-basic-boot
    newTag: dev-latest
  - name: trinax-contract-boot
    newTag: dev-latest
  - name: trinax-delivery-boot
    newTag: dev-latest
  - name: trinax-filebff
    newTag: dev-latest
  - name: trinax-integration-boot
    newTag: dev-latest
  - name: trinax-masterdata-boot
    newTag: dev-latest
  - name: trinax-partner-boot
    newTag: dev-latest
  - name: trinax-partnerbff
    newTag: dev-latest
  - name: trinax-report-boot
    newTag: dev-latest
  - name: trinax-sharedapi
    newTag: dev-latest
  - name: trinax-staffbff
    newTag: dev-latest
  - name: trinax-user-boot
    newTag: dev-latest
  - name: trinax-filegateway
    newTag: dev-latest
  - name: dtt-gateway
    newTag: dev-latest

replicas:
  - name: trinax-auth-center-boot
    count: 1
  - name: trinax-basic-boot
    count: 1
  - name: trinax-contract-boot
    count: 1
  - name: trinax-delivery-boot
    count: 1
  - name: trinax-filebff
    count: 1
  - name: trinax-integration-boot
    count: 1
  - name: trinax-masterdata-boot
    count: 1
  - name: trinax-partner-boot
    count: 1
  - name: trinax-partnerbff
    count: 1
  - name: trinax-report-boot
    count: 1
  - name: trinax-sharedapi
    count: 1
  - name: trinax-staffbff
    count: 1
  - name: trinax-user-boot
    count: 1
  - name: trinax-filegateway
    count: 1
  - name: dtt-gateway
    count: 1

configMapGenerator:
  - name: trinax-env-config
    literals:
      - ENVIRONMENT=development
      - LOG_LEVEL=DEBUG
      - TIMEZONE=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=dev

secretGenerator:
  - name: trinax-app-secrets
    literals:
      - API_KEY=your-dev-api-key-here
    type: Opaque

patchesStrategicMerge:
  - resource-limits-dev.yaml
