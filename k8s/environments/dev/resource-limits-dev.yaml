# 开发环境资源限制补丁 - 降低资源使用
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-auth-center-boot
spec:
  template:
    spec:
      containers:
      - name: trinax-auth-center-boot
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-basic-boot
spec:
  template:
    spec:
      containers:
      - name: trinax-basic-boot
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dtt-gateway
spec:
  template:
    spec:
      containers:
      - name: dtt-gateway
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "250m"
