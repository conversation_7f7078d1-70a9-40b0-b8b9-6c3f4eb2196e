apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: trinax-system

resources:
  - ../../

namePrefix: ""

commonLabels:
  environment: production

images:
  - name: trinax-auth-center-boot
    newTag: latest
  - name: trinax-basic-boot
    newTag: latest
  - name: trinax-contract-boot
    newTag: latest
  - name: trinax-delivery-boot
    newTag: latest
  - name: trinax-filebff
    newTag: latest
  - name: trinax-integration-boot
    newTag: latest
  - name: trinax-masterdata-boot
    newTag: latest
  - name: trinax-partner-boot
    newTag: latest
  - name: trinax-partnerbff
    newTag: latest
  - name: trinax-report-boot
    newTag: latest
  - name: trinax-sharedapi
    newTag: latest
  - name: trinax-staffbff
    newTag: latest
  - name: trinax-user-boot
    newTag: latest
  - name: trinax-filegateway
    newTag: latest
  - name: dtt-gateway
    newTag: latest

replicas:
  - name: trinax-auth-center-boot
    count: 2
  - name: trinax-basic-boot
    count: 2
  - name: trinax-contract-boot
    count: 2
  - name: trinax-delivery-boot
    count: 2
  - name: trinax-filebff
    count: 2
  - name: trinax-integration-boot
    count: 2
  - name: trinax-masterdata-boot
    count: 2
  - name: trinax-partner-boot
    count: 2
  - name: trinax-partnerbff
    count: 2
  - name: trinax-report-boot
    count: 2
  - name: trinax-sharedapi
    count: 2
  - name: trinax-staffbff
    count: 2
  - name: trinax-user-boot
    count: 2
  - name: trinax-filegateway
    count: 2
  - name: dtt-gateway
    count: 2

configMapGenerator:
  - name: trinax-env-config
    literals:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - TIMEZONE=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=k8s

secretGenerator:
  - name: trinax-app-secrets
    literals:
      - API_KEY=your-production-api-key-here
    type: Opaque

patchesStrategicMerge:
  - resource-limits-prod.yaml
