# 生产环境资源限制补丁 - 更高的资源配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-auth-center-boot
spec:
  template:
    spec:
      containers:
      - name: trinax-auth-center-boot
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1500m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-basic-boot
spec:
  template:
    spec:
      containers:
      - name: trinax-basic-boot
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1500m"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: dtt-gateway
spec:
  template:
    spec:
      containers:
      - name: dtt-gateway
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
