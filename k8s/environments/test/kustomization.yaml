apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: trinax-test

resources:
  - ../../

namePrefix: test-

commonLabels:
  environment: test

images:
  - name: trinax-auth-center-boot
    newTag: test-latest
  - name: trinax-basic-boot
    newTag: test-latest
  - name: trinax-contract-boot
    newTag: test-latest
  - name: trinax-delivery-boot
    newTag: test-latest
  - name: trinax-filebff
    newTag: test-latest
  - name: trinax-integration-boot
    newTag: test-latest
  - name: trinax-masterdata-boot
    newTag: test-latest
  - name: trinax-partner-boot
    newTag: test-latest
  - name: trinax-partnerbff
    newTag: test-latest
  - name: trinax-report-boot
    newTag: test-latest
  - name: trinax-sharedapi
    newTag: test-latest
  - name: trinax-staffbff
    newTag: test-latest
  - name: trinax-user-boot
    newTag: test-latest
  - name: trinax-filegateway
    newTag: test-latest
  - name: dtt-gateway
    newTag: test-latest

replicas:
  - name: trinax-auth-center-boot
    count: 1
  - name: trinax-basic-boot
    count: 1
  - name: trinax-contract-boot
    count: 1
  - name: trinax-delivery-boot
    count: 1
  - name: trinax-filebff
    count: 1
  - name: trinax-integration-boot
    count: 1
  - name: trinax-masterdata-boot
    count: 1
  - name: trinax-partner-boot
    count: 1
  - name: trinax-partnerbff
    count: 1
  - name: trinax-report-boot
    count: 1
  - name: trinax-sharedapi
    count: 1
  - name: trinax-staffbff
    count: 1
  - name: trinax-user-boot
    count: 1
  - name: trinax-filegateway
    count: 1
  - name: dtt-gateway
    count: 1

configMapGenerator:
  - name: trinax-env-config
    literals:
      - ENVIRONMENT=test
      - LOG_LEVEL=INFO
      - TIMEZONE=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=test

secretGenerator:
  - name: trinax-app-secrets
    literals:
      - API_KEY=your-test-api-key-here
    type: Opaque
