# 基础配置 - 包含所有通用资源
# 这个文件作为base层，供不同环境继承使用
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  # 基础资源
  - base/namespace.yaml

  # 中间件
  - middleware/mysql.yaml
  - middleware/redis.yaml

  # 配置
  - configs/configmaps.yaml

  # 应用部署
  - deployments/trinax-auth-center-boot.yaml
  - deployments/trinax-basic-boot.yaml
  - deployments/trinax-contract-boot.yaml
  - deployments/trinax-delivery-boot.yaml
  - deployments/trinax-filebff.yaml
  - deployments/trinax-integration-boot.yaml
  - deployments/trinax-masterdata-boot.yaml
  - deployments/trinax-partner-boot.yaml
  - deployments/trinax-partnerbff.yaml
  - deployments/trinax-report-boot.yaml
  - deployments/trinax-sharedapi.yaml
  - deployments/trinax-staffbff.yaml
  - deployments/trinax-user-boot.yaml
  - deployments/trinax-filegateway.yaml
  - deployments/dtt-gateway.yaml

commonLabels:
  project: trinax
