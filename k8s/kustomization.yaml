apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: trinax-system

resources:
  # 基础资源
  - base/namespace.yaml
  
  # 中间件
  - middleware/mysql.yaml
  - middleware/redis.yaml
  
  # 配置
  - configs/configmaps.yaml
  
  # 应用部署
  - deployments/trinax-auth-center-boot.yaml
  - deployments/dtt-gateway.yaml
  # 其他微服务部署文件...

images:
  - name: trinax-auth-center-boot
    newTag: latest
  - name: trinax-basic-boot
    newTag: latest
  - name: trinax-contract-boot
    newTag: latest
  - name: trinax-delivery-boot
    newTag: latest
  - name: trinax-filebff
    newTag: latest
  - name: trinax-integration-boot
    newTag: latest
  - name: trinax-masterdata-boot
    newTag: latest
  - name: trinax-partner-boot
    newTag: latest
  - name: trinax-partnerbff
    newTag: latest
  - name: trinax-report-boot
    newTag: latest
  - name: trinax-sharedapi
    newTag: latest
  - name: trinax-staffbff
    newTag: latest
  - name: trinax-user-boot
    newTag: latest
  - name: trinax-filegateway
    newTag: latest
  - name: dtt-gateway
    newTag: latest

commonLabels:
  project: trinax
  environment: production

patchesStrategicMerge:
  - patches/resource-limits.yaml

configMapGenerator:
  - name: trinax-env-config
    literals:
      - ENVIRONMENT=production
      - LOG_LEVEL=INFO
      - TIMEZONE=Asia/Shanghai

secretGenerator:
  - name: trinax-app-secrets
    literals:
      - API_KEY=your-api-key-here
    type: Opaque
