apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-integration-boot
  namespace: trinax-system
  labels:
    app: trinax-integration-boot
    component: backend
    tier: service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trinax-integration-boot
  template:
    metadata:
      labels:
        app: trinax-integration-boot
        component: backend
        tier: service
    spec:
      containers:
      - name: trinax-integration-boot
        image: trinax-integration-boot:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 7025
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MEMORY_LIMIT
          value: "1G"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 7025
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 7025
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /opt/app/config
          readOnly: true
        - name: logs
          mountPath: /opt/app/logs
      volumes:
      - name: app-config
        configMap:
          name: trinax-integration-config
      - name: logs
        emptyDir: {}
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: trinax-integration-boot-service
  namespace: trinax-system
  labels:
    app: trinax-integration-boot
spec:
  type: ClusterIP
  ports:
  - port: 7025
    targetPort: 7025
    protocol: TCP
    name: http
  selector:
    app: trinax-integration-boot
