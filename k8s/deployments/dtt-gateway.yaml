apiVersion: apps/v1
kind: Deployment
metadata:
  name: dtt-gateway
  namespace: trinax-system
  labels:
    app: dtt-gateway
    component: gateway
    tier: frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: dtt-gateway
  template:
    metadata:
      labels:
        app: dtt-gateway
        component: gateway
        tier: frontend
    spec:
      containers:
      - name: dtt-gateway
        image: dtt-gateway:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MEMORY_LIMIT
          value: "512M"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 80
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: gateway-config
          mountPath: /opt/app/config
          readOnly: true
      volumes:
      - name: gateway-config
        configMap:
          name: dtt-gateway-config
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: dtt-gateway-service
  namespace: trinax-system
  labels:
    app: dtt-gateway
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: dtt-gateway
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: trinax-ingress
  namespace: trinax-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
spec:
  ingressClassName: nginx
  rules:
  - host: trinax.local  # 替换为实际域名
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: dtt-gateway-service
            port:
              number: 80
