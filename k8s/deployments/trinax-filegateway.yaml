apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-filegateway
  namespace: trinax-system
  labels:
    app: trinax-filegateway
    component: backend
    tier: service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trinax-filegateway
  template:
    metadata:
      labels:
        app: trinax-filegateway
        component: backend
        tier: service
    spec:
      containers:
      - name: trinax-filegateway
        image: trinax-filegateway:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8001
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MEMORY_LIMIT
          value: "1G"
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: DB_USERNAME
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: DB_PASSWORD
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: trinax-secrets
              key: REDIS_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8001
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8001
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /opt/app/config
          readOnly: true
        - name: logs
          mountPath: /opt/app/logs
        - name: file-storage
          mountPath: /opt/app/files
      volumes:
      - name: app-config
        configMap:
          name: trinax-filegateway-config
      - name: logs
        emptyDir: {}
      - name: file-storage
        persistentVolumeClaim:
          claimName: trinax-file-storage-pvc
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: trinax-filegateway-service
  namespace: trinax-system
  labels:
    app: trinax-filegateway
spec:
  type: ClusterIP
  ports:
  - port: 8001
    targetPort: 8001
    protocol: TCP
    name: http
  selector:
    app: trinax-filegateway
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: trinax-file-storage-pvc
  namespace: trinax-system
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: standard
