apiVersion: apps/v1
kind: Deployment
metadata:
  name: trinax-auth-center-boot
  namespace: trinax-system
  labels:
    app: trinax-auth-center-boot
    component: backend
    tier: service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: trinax-auth-center-boot
  template:
    metadata:
      labels:
        app: trinax-auth-center-boot
        component: backend
        tier: service
    spec:
      containers:
      - name: trinax-auth-center-boot
        image: trinax-auth-center-boot:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 7020
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: MEMORY_LIMIT
          value: "1G"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 7020
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 7020
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        volumeMounts:
        - name: app-config
          mountPath: /opt/app/config
          readOnly: true
        - name: logs
          mountPath: /opt/app/logs
      volumes:
      - name: app-config
        configMap:
          name: trinax-auth-center-config
      - name: logs
        emptyDir: {}
      restartPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: trinax-auth-center-boot-service
  namespace: trinax-system
  labels:
    app: trinax-auth-center-boot
spec:
  type: ClusterIP
  ports:
  - port: 7020
    targetPort: 7020
    protocol: TCP
    name: http
  selector:
    app: trinax-auth-center-boot
