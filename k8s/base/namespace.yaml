apiVersion: v1
kind: Namespace
metadata:
  name: trinax-system
  labels:
    name: trinax-system
    environment: production
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: trinax-service-account
  namespace: trinax-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: trinax-system
  name: trinax-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: trinax-role-binding
  namespace: trinax-system
subjects:
- kind: ServiceAccount
  name: trinax-service-account
  namespace: trinax-system
roleRef:
  kind: Role
  name: trinax-role
  apiGroup: rbac.authorization.k8s.io
